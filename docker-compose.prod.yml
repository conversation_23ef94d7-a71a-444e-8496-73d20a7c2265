version: '3.8'

services:
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - NEXT_PUBLIC_API_URL=${FRONTEND_API_URL}
      - NEXT_PUBLIC_SOCKET_URL=${FRONTEND_SOCKET_URL}
      - NEXT_PUBLIC_DOMAIN=${DOMAIN}
    depends_on:
      - backend
    restart: unless-stopped

  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "8080:8080"
    volumes:
      - ./data:/app/data
      - ./uploads:/app/uploads
    environment:
      - PORT=8080
      - DB_PATH=/app/data/social_network.db
      - MIGRATIONS_PATH=/app/pkg/db/migrations/sqlite
      - AUTH_SECRET_KEY=${AUTH_SECRET_KEY}
      - ALLOWED_ORIGINS=${ALLOWED_ORIGINS}
      - DOMAIN=${DOMAIN}
      - SECURE_COOKIES=${SECURE_COOKIES:-true}
    restart: unless-stopped

  # Optional: Add a reverse proxy like nginx for production
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - frontend
      - backend
    restart: unless-stopped
    profiles:
      - with-nginx

volumes:
  data:
  uploads:
