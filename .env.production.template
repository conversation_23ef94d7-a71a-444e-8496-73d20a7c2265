# Production Environment Configuration Template
# Copy this file to .env.production and fill in your actual values

# Domain Configuration
DOMAIN=your-domain.com
FRONTEND_API_URL=https://your-domain.com/api
FRONTEND_SOCKET_URL=wss://your-domain.com/ws

# Backend Configuration
AUTH_SECRET_KEY=your-very-secure-random-secret-key-here-at-least-32-characters
ALLOWED_ORIGINS=https://your-domain.com,https://www.your-domain.com

# Security Settings
SECURE_COOKIES=true

# Database (if using external database in production)
# DB_PATH=/app/data/social_network.db

# Optional: If using external services
# REDIS_URL=redis://your-redis-instance
# SMTP_HOST=your-smtp-server
# SMTP_PORT=587
# SMTP_USER=<EMAIL>
# SMTP_PASS=your-email-password

# SSL/TLS Configuration (if using nginx)
# SSL_CERT_PATH=/etc/nginx/ssl/cert.pem
# SSL_KEY_PATH=/etc/nginx/ssl/key.pem
